#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF文件读取脚本
用于提取PDF文件中的文本内容
"""

import sys
import os

def read_pdf_with_pypdf2():
    """使用PyPDF2库读取PDF"""
    try:
        import PyPDF2
        
        with open('work.pdf', 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            print(f"PDF文件共有 {len(pdf_reader.pages)} 页")
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                page_text = page.extract_text()
                text += f"\n=== 第 {page_num + 1} 页 ===\n"
                text += page_text
                text += "\n"
            
            return text
    except ImportError:
        print("PyPDF2 库未安装")
        return None
    except Exception as e:
        print(f"使用PyPDF2读取PDF时出错: {e}")
        return None

def read_pdf_with_pdfplumber():
    """使用pdfplumber库读取PDF"""
    try:
        import pdfplumber
        
        text = ""
        with pdfplumber.open('work.pdf') as pdf:
            print(f"PDF文件共有 {len(pdf.pages)} 页")
            
            for page_num, page in enumerate(pdf.pages):
                page_text = page.extract_text()
                if page_text:
                    text += f"\n=== 第 {page_num + 1} 页 ===\n"
                    text += page_text
                    text += "\n"
        
        return text
    except ImportError:
        print("pdfplumber 库未安装")
        return None
    except Exception as e:
        print(f"使用pdfplumber读取PDF时出错: {e}")
        return None

def read_pdf_with_pymupdf():
    """使用PyMuPDF (fitz)库读取PDF"""
    try:
        import fitz  # PyMuPDF
        
        doc = fitz.open('work.pdf')
        text = ""
        
        print(f"PDF文件共有 {doc.page_count} 页")
        
        for page_num in range(doc.page_count):
            page = doc[page_num]
            page_text = page.get_text()
            text += f"\n=== 第 {page_num + 1} 页 ===\n"
            text += page_text
            text += "\n"
        
        doc.close()
        return text
    except ImportError:
        print("PyMuPDF (fitz) 库未安装")
        return None
    except Exception as e:
        print(f"使用PyMuPDF读取PDF时出错: {e}")
        return None

def main():
    """主函数"""
    print("正在尝试读取PDF文件...")
    
    # 检查文件是否存在
    if not os.path.exists('work.pdf'):
        print("错误: work.pdf 文件不存在")
        return
    
    # 尝试不同的PDF读取库
    methods = [
        ("PyMuPDF (fitz)", read_pdf_with_pymupdf),
        ("pdfplumber", read_pdf_with_pdfplumber),
        ("PyPDF2", read_pdf_with_pypdf2)
    ]
    
    text = None
    for method_name, method_func in methods:
        print(f"\n尝试使用 {method_name} 读取PDF...")
        text = method_func()
        if text:
            print(f"成功使用 {method_name} 读取PDF!")
            break
    
    if text:
        # 保存提取的文本到文件
        with open('extracted_text.txt', 'w', encoding='utf-8') as f:
            f.write(text)
        
        print(f"\n文本内容已保存到 extracted_text.txt")
        print(f"提取的文本长度: {len(text)} 字符")
        
        # 显示前1000个字符作为预览
        print("\n=== 文本内容预览 ===")
        print(text[:1000])
        if len(text) > 1000:
            print("...(内容过长，已截断)")
    else:
        print("\n所有PDF读取方法都失败了。请确保安装了相应的库:")
        print("pip install PyPDF2 pdfplumber PyMuPDF")

if __name__ == "__main__":
    main()
