
=== 第 1 页 ===
1. 基于知识图谱的应用实验（上）——基于 BM25 算
法和向量检索的知识检索实验
一、实验目的
本实验旨在利用 BM25 算法实现知识检索，从给定的知识（文档）数据中快速准确地找到与用户
查询相关的信息，同时探索知识图谱在知识检索中的应用潜力，提高检索的准确性和效率。
BM25算法介绍
公式
对于一个查询 Q = {q 1 ,q 2 ,⋯,q n } ，其中 q i 是查询中的第 i 个词，文档 D 与查询 Q 的相关性
得分 Score(Q,D) 计算公式如下： Score(Q,D) = ∑n i=1 IDF(q i )× f(q,D f ) ( + q k i ,D × ) ( × 1− (k b 1 + + b 1 × ) |D| )
i 1 avgdl
其中：
IDF(q
i
)
是词
q
i 的逆文档频率，用于衡量词
q
i 在整个文档集合中的稀有程度，计算公
式为 IDF(q i ) = log N n − ( n q ( ) q + i ) 0 + .5 0.5 ，N 是文档集合中的文档总数， n(q i ) 是包含词 q i 的文档
i
数量。
f(q i ,D) 是词 q i 在文档 D 中出现的频率。
k 1 和 b 是调整参数，通常 k 1 的取值范围在 1.2 到 2.0 之间，b 的取值通常在 0.75 左
右。
|D| 是文档 D 的长度（通常以词的数量为单位），avgdl 是文档集合中所有文档的平均
长度。
详细介绍
算法原理：BM25 算法基于概率模型，它考虑了查询词在文档中的出现频率、文档的长度以
及查询词在整个文档集合中的稀有程度等因素，来评估文档与查询的相关性。通过对这些因
素的综合考量，能够更准确地对检索结果进行排序，使得与查询相关性更高的文档排在前
面。
关键因素分析
词频（TF）：即 f(q i ,D) ，词在文档中出现的次数越多，说明该文档与查询词的相关性
可能越高。但 BM25 算法对词频进行了非线性的调整，避免单纯因为词频过高而过度影
响得分。
逆文档频率（IDF）： IDF(q i ) 反映了词的区分度。如果一个词在很多文档中都出现，
那么它的区分度就低，对文档相关性的贡献就小；反之，如果一个词只在少数文档中出
现，那么它的区分度就高，对文档相关性的贡献就大。

=== 第 2 页 ===
文档长度归一化：通过
1−b+b× |D|
这一项来实现。它考虑了文档长度对相关性得
avgdl
分的影响，避免长文档因为包含更多的词而在得分上占优势。如果文档长度与平均文档
长度相近，这一项的值接近 1；如果文档长度远大于平均文档长度，这一项的值会大于
1，从而抑制长文档的得分；如果文档长度远小于平均文档长度，这一项的值会小于 1，
适当提升短文档的得分。
二、实验环境
1. 硬件环境：普通计算机，配置满足 Python 程序运行要求。
2. 软件环境：Python 编程语言（建议使用 Python 3.x 版本），安装必要的第三方库 math 、
jieba 、 json 、 pickle 、 heapq 。
三、实验数据准备
1. 知识数据：使用 data_path.passages_multi_sentences 文件中存储的知识数据，该数据包
含多个文档，每个文档包含多个句子，并且每个文档有一个唯一的 pid 标识。
2. 停用词表：从 data_path.stopwords_file 文件中读取停用词，用于在文本处理过程中过滤
掉无意义的词汇。
3. 训练数据： data_path.train 文件中存储的训练数据，包含问题和对应的相关文档标识
（ pid ），用于评估检索模型的性能。
4. 测试数据： data_path.new_test 文件中存储的测试数据，包含问题，用于进行实际的知识
检索测试。
四、实验任务
1. 实现 BM25 模型：补全BM25.py中的代码，实现BM25模型
2. 实验结果分析： 在数据集上运行BM25算法，记录实验结果。
3. 基于SBERT实现稠密向量检索 ：参照SBERT示例，实现基于SBERT的向量检索，并在数据
集上运行记录实验结果。
4. 思考BM25的检索方法和向量检索的优劣。
