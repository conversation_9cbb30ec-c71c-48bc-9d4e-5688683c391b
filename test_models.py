#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试BM25和SBERT模型的脚本
"""

import pickle
import json
import time
import heapq
from BM25 import dealwords, BM25, SBERT
import data_path

def test_bm25_model():
    """测试BM25模型"""
    print("=" * 50)
    print("测试BM25模型")
    print("=" * 50)
    
    try:
        # 加载BM25模型
        with open(data_path.BM25Model, "rb") as f:
            bm25_model = pickle.load(f)
        print("✓ BM25模型加载成功")
        
        # 测试查询
        test_queries = [
            "日莲给他的弟子写了什么？",
            "腾讯公司的总部在哪里？",
            "黄山香烟是哪年创牌的？"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- 测试查询 {i}: {query} ---")
            start_time = time.time()
            
            # 处理查询
            query_words = dealwords(query)
            print(f"分词结果: {list(query_words)}")
            
            # 获取相关性得分
            scores = bm25_model.get_scores(query_words)
            
            # 获取top-3结果
            top_scores = heapq.nlargest(3, scores)
            top_indices = [scores.index(score) for score in top_scores]
            
            end_time = time.time()
            
            print(f"检索时间: {end_time - start_time:.4f}秒")
            print("Top-3 结果:")
            for j, (idx, score) in enumerate(zip(top_indices, top_scores), 1):
                print(f"  {j}. 文档ID: {idx}, 得分: {score:.4f}")
            
    except Exception as e:
        print(f"✗ BM25模型测试失败: {e}")

def test_sbert_model():
    """测试SBERT模型"""
    print("\n" + "=" * 50)
    print("测试SBERT模型")
    print("=" * 50)
    
    try:
        # 加载SBERT模型
        with open(data_path.SBERTModel, "rb") as f:
            sbert_model = pickle.load(f)
        print("✓ SBERT模型加载成功")
        
        # 测试查询
        test_queries = [
            "日莲给他的弟子写了什么？",
            "腾讯公司的总部在哪里？",
            "黄山香烟是哪年创牌的？"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n--- 测试查询 {i}: {query} ---")
            start_time = time.time()
            
            # 处理查询
            query_words = dealwords(query)
            print(f"分词结果: {list(query_words)}")
            
            # 获取相关性得分
            scores = sbert_model.get_scores(query_words)
            
            # 获取top-3结果
            top_scores = heapq.nlargest(3, scores)
            top_indices = [scores.index(score) for score in top_scores]
            
            end_time = time.time()
            
            print(f"检索时间: {end_time - start_time:.4f}秒")
            print("Top-3 结果:")
            for j, (idx, score) in enumerate(zip(top_indices, top_scores), 1):
                print(f"  {j}. 文档ID: {idx}, 得分: {score:.4f}")
            
    except Exception as e:
        print(f"✗ SBERT模型测试失败: {e}")

def compare_models():
    """比较两个模型的性能"""
    print("\n" + "=" * 50)
    print("模型性能比较")
    print("=" * 50)
    
    try:
        # 加载模型
        with open(data_path.BM25Model, "rb") as f:
            bm25_model = pickle.load(f)
        with open(data_path.SBERTModel, "rb") as f:
            sbert_model = pickle.load(f)
        
        query = "日莲给他的弟子写了什么？"
        query_words = dealwords(query)
        
        print(f"测试查询: {query}")
        print(f"分词结果: {list(query_words)}")
        
        # BM25检索
        print("\n--- BM25检索结果 ---")
        start_time = time.time()
        bm25_scores = bm25_model.get_scores(query_words)
        bm25_time = time.time() - start_time
        
        bm25_top_scores = heapq.nlargest(5, bm25_scores)
        bm25_top_indices = [bm25_scores.index(score) for score in bm25_top_scores]
        
        print(f"检索时间: {bm25_time:.4f}秒")
        for i, (idx, score) in enumerate(zip(bm25_top_indices, bm25_top_scores), 1):
            print(f"  {i}. 文档ID: {idx}, 得分: {score:.4f}")
        
        # SBERT检索
        print("\n--- SBERT检索结果 ---")
        start_time = time.time()
        sbert_scores = sbert_model.get_scores(query_words)
        sbert_time = time.time() - start_time
        
        sbert_top_scores = heapq.nlargest(5, sbert_scores)
        sbert_top_indices = [sbert_scores.index(score) for score in sbert_top_scores]
        
        print(f"检索时间: {sbert_time:.4f}秒")
        for i, (idx, score) in enumerate(zip(sbert_top_indices, sbert_top_scores), 1):
            print(f"  {i}. 文档ID: {idx}, 得分: {score:.4f}")
        
        # 性能比较
        print(f"\n--- 性能比较 ---")
        print(f"BM25检索时间: {bm25_time:.4f}秒")
        print(f"SBERT检索时间: {sbert_time:.4f}秒")
        print(f"速度比 (BM25/SBERT): {bm25_time/sbert_time:.2f}")
        
    except Exception as e:
        print(f"✗ 模型比较失败: {e}")

if __name__ == "__main__":
    print("开始测试检索模型...")
    
    # 测试BM25模型
    test_bm25_model()
    
    # 测试SBERT模型
    test_sbert_model()
    
    # 比较模型性能
    compare_models()
    
    print("\n测试完成！")
