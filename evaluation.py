#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正数据集评估逻辑
"""

import pickle
import json
import time
import heapq
import numpy as np
from BM25 import dealwords, BM25, SBERT
import data_path

def analyze_data_format():
    """分析数据格式，理解正确的评估方法"""
    print("分析数据格式...")
    
    # 加载训练数据
    with open(data_path.dataset, encoding='utf-8') as f:
        train_data = [json.loads(line.strip()) for line in f.readlines()]
    
    # 加载测试数据
    with open(data_path.new_test, encoding='utf-8') as f:
        test_data = [json.loads(line.strip()) for line in f.readlines()]
    
    print("训练数据格式分析:")
    sample = train_data[0]
    print(json.dumps(sample, ensure_ascii=False, indent=2))
    print(f"训练数据字段: {list(sample.keys())}")
    
    print("\n测试数据格式分析:")
    sample = test_data[0]
    print(json.dumps(sample, ensure_ascii=False, indent=2))
    print(f"测试数据字段: {list(sample.keys())}")
    
    # 分析训练数据中的pid字段
    print(f"\n训练数据中的pid范围:")
    pids = [item['pid'] for item in train_data[:100]]
    print(f"前100个pid: {pids[:10]}...")
    print(f"pid最小值: {min(pids)}, 最大值: {max(pids)}")
    
    # 分析测试数据中的pid字段
    print(f"\n测试数据中的pid范围:")
    pids = [item['pid'] for item in test_data[:100]]
    print(f"前100个pid: {pids[:10]}...")
    print(f"pid最小值: {min(pids)}, 最大值: {max(pids)}")
    
    return train_data, test_data

def correct_evaluation():
    """使用正确的评估方法"""
    print("\n" + "="*60)
    print("使用正确的评估方法重新评估")
    print("="*60)
    
    # 分析数据格式
    train_data, test_data = analyze_data_format()
    
    # 加载BM25模型
    with open(data_path.BM25Model, "rb") as f:
        bm25_model = pickle.load(f)
    print("✓ BM25模型加载成功")
    
    # 创建SBERT实例并加载模型
    sbert_model = SBERT([])
    sbert_model.load_model(data_path.SBERTModel)
    print("✓ SBERT模型加载成功")
    
    # 在训练数据上正确评估
    print(f"\n--- 在训练数据上正确评估BM25 (前50条) ---")
    bm25_train_results = evaluate_correctly(bm25_model, train_data[:50], "BM25-训练集")
    
    print(f"\n--- 在训练数据上正确评估SBERT (前50条) ---")
    sbert_train_results = evaluate_correctly(sbert_model, train_data[:50], "SBERT-训练集")
    
    # 在测试数据上评估（测试数据有正确答案）
    print(f"\n--- 在测试数据上正确评估BM25 (前50条) ---")
    bm25_test_results = evaluate_correctly(bm25_model, test_data[:50], "BM25-测试集")
    
    print(f"\n--- 在测试数据上正确评估SBERT (前50条) ---")
    sbert_test_results = evaluate_correctly(sbert_model, test_data[:50], "SBERT-测试集")
    
    # 对比结果
    print(f"\n{'='*60}")
    print("修正后的评估结果对比")
    print(f"{'='*60}")
    
    print("训练集结果:")
    print(f"  BM25: Top-1={bm25_train_results['top1_accuracy']:.4f}, Top-5={bm25_train_results['top5_accuracy']:.4f}, 时间={bm25_train_results['avg_time']:.4f}s")
    print(f"  SBERT: Top-1={sbert_train_results['top1_accuracy']:.4f}, Top-5={sbert_train_results['top5_accuracy']:.4f}, 时间={sbert_train_results['avg_time']:.4f}s")
    
    print("测试集结果:")
    print(f"  BM25: Top-1={bm25_test_results['top1_accuracy']:.4f}, Top-5={bm25_test_results['top5_accuracy']:.4f}, 时间={bm25_test_results['avg_time']:.4f}s")
    print(f"  SBERT: Top-1={sbert_test_results['top1_accuracy']:.4f}, Top-5={sbert_test_results['top5_accuracy']:.4f}, 时间={sbert_test_results['avg_time']:.4f}s")
    
    return {
        'bm25_train': bm25_train_results,
        'sbert_train': sbert_train_results,
        'bm25_test': bm25_test_results,
        'sbert_test': sbert_test_results
    }

def evaluate_correctly(model, dataset, model_name):
    """正确的评估方法"""
    total_queries = len(dataset)
    total_time = 0
    top1_hits = 0
    top5_hits = 0
    top10_hits = 0
    valid_queries = 0
    
    print(f"正确评估 {model_name}，共 {total_queries} 个查询...")
    
    for i, item in enumerate(dataset):
        if i % 20 == 0:
            print(f"进度: {i}/{total_queries}")
        
        query = item.get('question', '')
        if not query:
            continue
        
        # 获取正确答案的文档ID
        correct_pid = item.get('pid', -1)
        if correct_pid == -1:
            continue
            
        valid_queries += 1
        
        # 处理查询
        query_words = dealwords(query)
        
        # 计时检索
        start_time = time.time()
        scores = model.get_scores(query_words)
        end_time = time.time()
        
        search_time = end_time - start_time
        total_time += search_time
        
        # 获取top-10结果
        top_scores = heapq.nlargest(10, scores)
        top_indices = [scores.index(score) for score in top_scores]
        
        # 计算命中率 - 检查正确的文档ID是否在top结果中
        if len(top_indices) > 0 and top_indices[0] == correct_pid:
            top1_hits += 1
        
        if correct_pid in top_indices[:5]:
            top5_hits += 1
            
        if correct_pid in top_indices[:10]:
            top10_hits += 1
        
        # 显示前几个查询的详细结果
        if i < 5:
            print(f"\n查询 {i+1}: {query}")
            print(f"  正确答案文档ID: {correct_pid}")
            print(f"  Top-5检索结果: {top_indices[:5]}")
            print(f"  是否命中Top-1: {'是' if top_indices[0] == correct_pid else '否'}")
            print(f"  是否命中Top-5: {'是' if correct_pid in top_indices[:5] else '否'}")
    
    # 计算统计指标
    if valid_queries > 0:
        avg_time = total_time / valid_queries
        top1_accuracy = top1_hits / valid_queries
        top5_accuracy = top5_hits / valid_queries
        top10_accuracy = top10_hits / valid_queries
    else:
        avg_time = top1_accuracy = top5_accuracy = top10_accuracy = 0
    
    results = {
        'model_name': model_name,
        'total_queries': total_queries,
        'valid_queries': valid_queries,
        'avg_time': avg_time,
        'top1_hits': top1_hits,
        'top5_hits': top5_hits,
        'top10_hits': top10_hits,
        'top1_accuracy': top1_accuracy,
        'top5_accuracy': top5_accuracy,
        'top10_accuracy': top10_accuracy
    }
    
    print(f"\n{model_name}评估结果:")
    print(f"  有效查询数: {valid_queries}/{total_queries}")
    print(f"  平均检索时间: {avg_time:.4f}秒")
    print(f"  Top-1 准确率: {top1_accuracy:.4f} ({top1_hits}/{valid_queries})")
    print(f"  Top-5 准确率: {top5_accuracy:.4f} ({top5_hits}/{valid_queries})")
    print(f"  Top-10 准确率: {top10_accuracy:.4f} ({top10_hits}/{valid_queries})")
    
    return results

def save_corrected_results(results):
    """保存修正后的结果"""
    with open('corrected_evaluation_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"\n修正后的评估结果已保存到 corrected_evaluation_results.json")

if __name__ == "__main__":
    print("开始修正数据集评估...")
    
    # 正确评估
    results = correct_evaluation()
    
    # 保存结果
    save_corrected_results(results)
    
    print("\n修正评估完成！")
