{"bm25_train": {"model_name": "BM25-训练集", "total_queries": 50, "valid_queries": 50, "avg_time": 0.015930824279785157, "top1_hits": 44, "top5_hits": 47, "top10_hits": 47, "top1_accuracy": 0.88, "top5_accuracy": 0.94, "top10_accuracy": 0.94}, "sbert_train": {"model_name": "SBERT-训练集", "total_queries": 50, "valid_queries": 50, "avg_time": 0.03489048957824707, "top1_hits": 18, "top5_hits": 24, "top10_hits": 26, "top1_accuracy": 0.36, "top5_accuracy": 0.48, "top10_accuracy": 0.52}, "bm25_test": {"model_name": "BM25-测试集", "total_queries": 50, "valid_queries": 50, "avg_time": 0.01631410598754883, "top1_hits": 50, "top5_hits": 50, "top10_hits": 50, "top1_accuracy": 1.0, "top5_accuracy": 1.0, "top10_accuracy": 1.0}, "sbert_test": {"model_name": "SBERT-测试集", "total_queries": 50, "valid_queries": 50, "avg_time": 0.034114346504211426, "top1_hits": 14, "top5_hits": 22, "top10_hits": 25, "top1_accuracy": 0.28, "top5_accuracy": 0.44, "top10_accuracy": 0.5}}