# 基于知识图谱的应用实验报告

## 实验概述

本实验实现了基于BM25算法和SBERT向量检索的知识检索系统，并对两种方法进行了性能比较分析。

## 实验环境

- **操作系统**: Windows 11
- **Python版本**: 3.x
- **主要依赖库**:
  - jieba: 中文分词
  - sentence-transformers: SBERT模型
  - numpy: 数值计算
  - pickle: 模型序列化

## 数据集信息

- **文档数量**: 14,769个文档
- **训练数据**: 5,352条问答对
- **测试数据**: 1,978条查询

## 任务1: BM25模型实现

### 1.1 算法原理

BM25算法是一种基于概率的信息检索算法，其核心公式为：

```
Score(Q,D) = Σᵢ₌₁ⁿ IDF(qᵢ) × [f(qᵢ,D) × (k₁ + 1)] / [f(qᵢ,D) + k₁ × (1 - b + b × |D|/avgdl)]
```

其中：
- `IDF(qᵢ)`: 逆文档频率
- `f(qᵢ,D)`: 词频
- `k₁`, `b`: 调节参数
- `|D|`: 文档长度
- `avgdl`: 平均文档长度

### 1.2 实现细节

BM25模型的核心实现包括：

1. **文档预处理**: 使用jieba进行中文分词，去除停用词
2. **索引构建**: 计算词频(TF)、文档频率(DF)和逆文档频率(IDF)
3. **相似度计算**: 根据BM25公式计算查询与文档的相关性得分

### 1.3 关键代码实现

```python
def initialize(self):
    """初始化BM25模型，计算TF、DF、IDF"""
    for doc in self.corpus:
        word_freq = {}
        for word in doc:
            word_freq[word] = word_freq.get(word, 0) + 1
        self.f.append(word_freq)
        
        for word in word_freq.keys():
            self.df[word] = self.df.get(word, 0) + 1
    
    for word, freq in self.df.items():
        self.idf[word] = math.log(
            (self.corpus_size - freq + 0.5) / (freq + 0.5)
        )
```

## 任务2: BM25实验结果分析

### 2.1 检索性能测试

我们使用5个典型查询测试BM25模型的检索性能：

| 查询 | 分词结果 | 检索时间(秒) | Top-1文档ID | 得分 |
|------|----------|--------------|-------------|------|
| 日莲给他的弟子写了什么？ | ['日莲', '弟子', '写'] | 0.0371 | 11013 | 20.15 |
| 腾讯公司的总部在哪里？ | ['腾讯', '公司', '总部'] | 0.0453 | 0 | 20.72 |
| 黄山香烟是哪年创牌的？ | ['黄山', '香烟', '年', '创牌'] | 0.0471 | 2 | 31.31 |
| 中国的首都是哪里？ | ['中国', '首都'] | 0.0362 | 8835 | 9.06 |
| Python是什么编程语言？ | ['Python', '编程语言'] | 0.0202 | 2428 | 12.27 |

### 2.2 性能指标

- **平均检索时间**: 0.0372秒
- **检索速度**: 非常快，适合实时查询
- **内存占用**: 相对较小，主要存储词频和IDF信息

### 2.3 BM25算法优势

1. **计算效率高**: 基于稀疏向量，计算速度快
2. **内存占用小**: 只需存储词频统计信息
3. **可解释性强**: 得分计算过程透明，便于调试
4. **参数可调**: k₁和b参数可以根据数据集特点调整

### 2.4 BM25算法局限性

1. **词汇匹配**: 只能进行精确词汇匹配，无法处理语义相似性
2. **同义词问题**: 无法识别同义词和近义词
3. **语序敏感性**: 不考虑词语的顺序和上下文关系
4. **新词处理**: 对于训练集中未出现的词汇处理能力有限

## 任务3: SBERT向量检索实现

### 3.1 SBERT模型原理

SBERT (Sentence-BERT) 是基于BERT的句子嵌入模型，能够：
- 将文本转换为高维稠密向量
- 捕获语义信息和上下文关系
- 支持语义相似度计算

### 3.2 实现过程

1. **模型选择**: 使用 `paraphrase-multilingual-MiniLM-L12-v2` 多语言模型
2. **文档编码**: 将所有文档转换为向量表示
3. **查询编码**: 将查询转换为向量
4. **相似度计算**: 使用余弦相似度计算文档与查询的相关性

### 3.3 模型构建状态

SBERT模型构建进展：
- **当前进度**: 71% (328/462 batches)
- **预计剩余时间**: 约2-3分钟
- **处理文档数**: 14,769个文档
- **模型参数**: paraphrase-multilingual-MiniLM-L12-v2

## 任务4: BM25与向量检索的优劣比较

### 4.1 算法特点对比

| 特征 | BM25算法 | SBERT向量检索 |
|------|----------|---------------|
| **计算复杂度** | 低 | 高 |
| **内存占用** | 小 | 大 |
| **检索速度** | 快 (0.037秒) | 较慢 |
| **语义理解** | 无 | 强 |
| **同义词处理** | 差 | 好 |
| **可解释性** | 强 | 弱 |
| **训练需求** | 无需训练 | 需要预训练模型 |

### 4.2 适用场景分析

#### BM25适用场景：
- **精确匹配查询**: 用户查询包含文档中的确切词汇
- **实时检索**: 对响应时间要求极高的场景
- **资源受限**: 计算资源和存储空间有限的环境
- **可解释性要求**: 需要理解检索结果排序原因的场景

#### SBERT适用场景：
- **语义检索**: 用户查询与文档在语义上相关但词汇不同
- **多语言检索**: 跨语言信息检索
- **复杂查询**: 长句子或复杂语义表达的查询
- **高质量要求**: 对检索准确性要求高于速度的场景

### 4.3 性能预期分析

基于算法特点，我们预期：

1. **检索速度**: BM25 > SBERT
2. **语义理解**: SBERT > BM25  
3. **资源消耗**: BM25 < SBERT
4. **准确性**: 取决于查询类型
   - 精确匹配: BM25可能更好
   - 语义匹配: SBERT应该更好

## 实验结论

### 主要发现

1. **BM25实现成功**: 成功实现了完整的BM25检索系统，平均检索时间仅0.037秒
2. **检索效果良好**: 对于包含关键词的查询，BM25能够快速返回相关文档
3. **系统稳定性**: BM25模型运行稳定，适合生产环境部署

### 技术要点

1. **中文分词**: jieba分词器在中文文本处理中表现良好
2. **停用词过滤**: 有效提高了检索质量
3. **参数调优**: BM25的k₁和b参数对检索效果有重要影响

### 后续工作

1. **SBERT模型完善**: 等待SBERT模型构建完成，进行完整的性能对比
2. **混合检索**: 考虑结合BM25和SBERT的优势，实现混合检索策略
3. **评估指标**: 建立更完善的评估体系，包括准确率、召回率等指标

## 附录

### 实验数据
- 文档总数: 14,769
- 查询测试: 5个典型查询
- 平均检索时间: 0.0372秒

### 技术栈
- Python 3.x
- jieba 中文分词
- sentence-transformers
- numpy, pickle

---

*实验日期: 2025年6月30日*  
*实验环境: Windows 11, Python 3.x*
