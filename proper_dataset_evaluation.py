#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在数据集上正确运行BM25和SBERT算法，记录实验结果
"""

import pickle
import json
import time
import heapq
import numpy as np
from collections import defaultdict
from BM25 import dealwords, BM25, SBERT
import data_path

def load_and_examine_data():
    """加载并检查数据格式"""
    print("正在加载和检查数据格式...")
    
    # 加载训练数据
    with open(data_path.dataset, encoding='utf-8') as f:
        train_data = [json.loads(line.strip()) for line in f.readlines()]
    
    # 加载测试数据
    with open(data_path.new_test, encoding='utf-8') as f:
        test_data = [json.loads(line.strip()) for line in f.readlines()]
    
    print(f"训练数据: {len(train_data)} 条")
    print(f"测试数据: {len(test_data)} 条")
    
    # 检查数据格式
    print("\n训练数据样例:")
    if train_data:
        print(json.dumps(train_data[0], ensure_ascii=False, indent=2))
    
    print("\n测试数据样例:")
    if test_data:
        print(json.dumps(test_data[0], ensure_ascii=False, indent=2))
    
    return train_data, test_data

def evaluate_bm25_on_dataset(train_data, test_data):
    """在数据集上评估BM25模型"""
    print("\n" + "="*60)
    print("在数据集上运行BM25算法")
    print("="*60)
    
    # 加载BM25模型
    with open(data_path.BM25Model, "rb") as f:
        bm25_model = pickle.load(f)
    print("✓ BM25模型加载成功")
    
    # 在训练数据上评估
    print(f"\n--- 在训练数据上运行BM25 (前200条) ---")
    train_results = run_model_on_dataset(bm25_model, train_data[:200], "训练集", has_answers=True)
    
    # 在测试数据上运行（测试数据可能没有标准答案）
    print(f"\n--- 在测试数据上运行BM25 (前100条) ---")
    test_results = run_model_on_dataset(bm25_model, test_data[:100], "测试集", has_answers=False)
    
    return {
        'model': 'BM25',
        'train_results': train_results,
        'test_results': test_results
    }

def evaluate_sbert_on_dataset(train_data, test_data):
    """在数据集上评估SBERT模型"""
    print("\n" + "="*60)
    print("在数据集上运行SBERT算法")
    print("="*60)
    
    try:
        # 创建SBERT实例并加载模型
        sbert_model = SBERT([])
        sbert_model.load_model(data_path.SBERTModel)
        print("✓ SBERT模型加载成功")
        
        # 在训练数据上评估
        print(f"\n--- 在训练数据上运行SBERT (前200条) ---")
        train_results = run_model_on_dataset(sbert_model, train_data[:200], "训练集", has_answers=True)
        
        # 在测试数据上运行
        print(f"\n--- 在测试数据上运行SBERT (前100条) ---")
        test_results = run_model_on_dataset(sbert_model, test_data[:100], "测试集", has_answers=False)
        
        return {
            'model': 'SBERT',
            'train_results': train_results,
            'test_results': test_results
        }
        
    except Exception as e:
        print(f"✗ SBERT模型评估失败: {e}")
        return None

def run_model_on_dataset(model, dataset, dataset_name, has_answers=True):
    """在数据集上运行模型"""
    total_queries = len(dataset)
    total_time = 0
    top1_hits = 0
    top5_hits = 0
    top10_hits = 0
    valid_queries = 0
    
    print(f"在{dataset_name}上运行，共 {total_queries} 个查询...")
    
    results_detail = []
    
    for i, item in enumerate(dataset):
        if i % 50 == 0:
            print(f"进度: {i}/{total_queries}")
        
        query = item.get('question', '')
        if not query:
            continue
            
        valid_queries += 1
        
        # 获取相关文档ID（如果有的话）
        relevant_pids = []
        if has_answers and 'answers' in item:
            relevant_pids = [ans['pid'] for ans in item['answers']]
        
        # 处理查询
        query_words = dealwords(query)
        
        # 计时检索
        start_time = time.time()
        scores = model.get_scores(query_words)
        end_time = time.time()
        
        search_time = end_time - start_time
        total_time += search_time
        
        # 获取top-10结果
        top_scores = heapq.nlargest(10, scores)
        top_indices = [scores.index(score) for score in top_scores]
        
        # 记录详细结果
        query_result = {
            'query_id': i,
            'query': query,
            'search_time': search_time,
            'top_10_docs': [(idx, score) for idx, score in zip(top_indices, top_scores)],
            'relevant_pids': relevant_pids
        }
        results_detail.append(query_result)
        
        # 计算命中率（只有当有标准答案时）
        if has_answers and relevant_pids:
            if len(top_indices) > 0 and top_indices[0] in relevant_pids:
                top1_hits += 1
            
            if any(idx in relevant_pids for idx in top_indices[:5]):
                top5_hits += 1
                
            if any(idx in relevant_pids for idx in top_indices[:10]):
                top10_hits += 1
    
    # 计算统计指标
    avg_time = total_time / valid_queries if valid_queries > 0 else 0
    
    if has_answers and valid_queries > 0:
        top1_accuracy = top1_hits / valid_queries
        top5_accuracy = top5_hits / valid_queries
        top10_accuracy = top10_hits / valid_queries
    else:
        top1_accuracy = top5_accuracy = top10_accuracy = None
    
    results = {
        'dataset_name': dataset_name,
        'total_queries': total_queries,
        'valid_queries': valid_queries,
        'total_time': total_time,
        'avg_time': avg_time,
        'top1_hits': top1_hits,
        'top5_hits': top5_hits,
        'top10_hits': top10_hits,
        'top1_accuracy': top1_accuracy,
        'top5_accuracy': top5_accuracy,
        'top10_accuracy': top10_accuracy,
        'details': results_detail[:10]  # 只保存前10个详细结果
    }
    
    # 打印结果
    print(f"\n{dataset_name}评估结果:")
    print(f"  总查询数: {total_queries}")
    print(f"  有效查询数: {valid_queries}")
    print(f"  总检索时间: {total_time:.4f}秒")
    print(f"  平均检索时间: {avg_time:.4f}秒")
    
    if has_answers:
        print(f"  Top-1 命中数: {top1_hits}")
        print(f"  Top-5 命中数: {top5_hits}")
        print(f"  Top-10 命中数: {top10_hits}")
        print(f"  Top-1 准确率: {top1_accuracy:.4f}")
        print(f"  Top-5 准确率: {top5_accuracy:.4f}")
        print(f"  Top-10 准确率: {top10_accuracy:.4f}")
    
    return results

def save_results(bm25_results, sbert_results):
    """保存实验结果"""
    all_results = {
        'experiment_date': '2025-06-30',
        'dataset_info': {
            'total_documents': 14769,
            'train_queries_tested': 200,
            'test_queries_tested': 100
        },
        'bm25_results': bm25_results,
        'sbert_results': sbert_results
    }
    
    with open('dataset_evaluation_results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n实验结果已保存到 dataset_evaluation_results.json")

def main():
    """主函数"""
    print("开始在数据集上运行BM25和SBERT算法...")
    
    # 加载和检查数据
    train_data, test_data = load_and_examine_data()
    
    # 在数据集上运行BM25
    bm25_results = evaluate_bm25_on_dataset(train_data, test_data)
    
    # 在数据集上运行SBERT
    sbert_results = evaluate_sbert_on_dataset(train_data, test_data)
    
    # 保存结果
    save_results(bm25_results, sbert_results)
    
    # 对比分析
    print("\n" + "="*60)
    print("数据集评估结果对比")
    print("="*60)
    
    if bm25_results and sbert_results:
        bm25_train = bm25_results['train_results']
        sbert_train = sbert_results['train_results']
        
        print(f"训练集评估对比:")
        print(f"  BM25平均检索时间: {bm25_train['avg_time']:.4f}秒")
        print(f"  SBERT平均检索时间: {sbert_train['avg_time']:.4f}秒")
        print(f"  速度比 (SBERT/BM25): {sbert_train['avg_time']/bm25_train['avg_time']:.2f}x")
        
        if bm25_train['top1_accuracy'] is not None:
            print(f"  BM25 Top-1准确率: {bm25_train['top1_accuracy']:.4f}")
            print(f"  BM25 Top-5准确率: {bm25_train['top5_accuracy']:.4f}")
            print(f"  BM25 Top-10准确率: {bm25_train['top10_accuracy']:.4f}")
        
        if sbert_train['top1_accuracy'] is not None:
            print(f"  SBERT Top-1准确率: {sbert_train['top1_accuracy']:.4f}")
            print(f"  SBERT Top-5准确率: {sbert_train['top5_accuracy']:.4f}")
            print(f"  SBERT Top-10准确率: {sbert_train['top10_accuracy']:.4f}")
    
    print("\n数据集评估完成！")

if __name__ == "__main__":
    main()
