#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新构建SBERT模型的脚本
"""

import json
import pickle
import time
from BM25 import dealwords, SBERT
import data_path

def rebuild_sbert_model():
    """重新构建SBERT模型"""
    print("开始重新构建SBERT模型...")
    
    docs = []  # 所有文档列表,词表示
    
    # 读取文件
    print("正在读取文档数据...")
    with open(data_path.passages_multi_sentences, encoding='utf-8') as fin:
        read_results = [json.loads(line.strip()) for line in fin.readlines()]

    print(f"共读取 {len(read_results)} 个文档")
    
    # 处理文档
    for i, result in enumerate(read_results):
        if i % 1000 == 0:
            print(f"处理进度: {i}/{len(read_results)}")
            
        words_in_document = []
        for sent in result['document']:
            for word in dealwords(sent):  # 去停用词
                words_in_document.append(word)
        docs.append(words_in_document)

    print(f"文档处理完成，共 {len(docs)} 个文档")
    print("开始建立SBERT模型...")
    
    start_time = time.time()
    sbert_model = SBERT(docs)
    end_time = time.time()
    
    print(f"SBERT模型构建完成，耗时: {end_time - start_time:.2f}秒")
    
    # 保存模型
    print("正在保存SBERT模型...")
    sbert_model.save_model(data_path.SBERTModel)
    print("SBERT模型保存完成！")

if __name__ == "__main__":
    rebuild_sbert_model()
