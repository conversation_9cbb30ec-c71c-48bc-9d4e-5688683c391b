#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的实验分析脚本 - BM25和SBERT模型完整对比
"""

import pickle
import json
import time
import heapq
import numpy as np
from collections import defaultdict
from BM25 import dealwords, BM25, SBERT
import data_path

def test_both_models():
    """测试两个模型的完整性能"""
    print("="*80)
    print("完整的BM25和SBERT模型性能对比测试")
    print("="*80)
    
    # 加载模型
    print("正在加载模型...")
    try:
        with open(data_path.BM25Model, "rb") as f:
            bm25_model = pickle.load(f)
        print("✓ BM25模型加载成功")
    except Exception as e:
        print(f"✗ BM25模型加载失败: {e}")
        return
    
    try:
        # 创建新的SBERT实例并加载模型
        sbert_model = SBERT([])  # 创建空实例
        sbert_model.load_model(data_path.SBERTModel)
        print("✓ SBERT模型加载成功")
    except Exception as e:
        print(f"✗ SBERT模型加载失败: {e}")
        return
    
    # 测试查询
    test_queries = [
        "日莲给他的弟子写了什么？",
        "腾讯公司的总部在哪里？", 
        "黄山香烟是哪年创牌的？",
        "中国的首都是哪里？",
        "Python是什么编程语言？",
        "人工智能的发展历史",
        "机器学习算法有哪些？",
        "深度学习和神经网络的关系"
    ]
    
    print(f"\n测试查询数量: {len(test_queries)}")
    print("查询列表:")
    for i, query in enumerate(test_queries, 1):
        print(f"  {i}. {query}")
    
    # 性能对比测试
    bm25_times = []
    sbert_times = []
    
    print(f"\n{'='*20} BM25检索结果 {'='*20}")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- 查询 {i}: {query} ---")
        query_words = dealwords(query)
        print(f"分词结果: {list(query_words)}")
        
        # BM25检索
        start_time = time.time()
        bm25_scores = bm25_model.get_scores(query_words)
        bm25_time = time.time() - start_time
        bm25_times.append(bm25_time)
        
        bm25_top_scores = heapq.nlargest(3, bm25_scores)
        bm25_top_indices = [bm25_scores.index(score) for score in bm25_top_scores]
        
        print(f"BM25检索时间: {bm25_time:.4f}秒")
        print("BM25 Top-3 结果:")
        for j, (idx, score) in enumerate(zip(bm25_top_indices, bm25_top_scores), 1):
            print(f"  {j}. 文档ID: {idx}, 得分: {score:.4f}")
    
    print(f"\n{'='*20} SBERT检索结果 {'='*20}")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- 查询 {i}: {test_queries[i-1]} ---")
        query_words = dealwords(test_queries[i-1])
        
        # SBERT检索
        start_time = time.time()
        sbert_scores = sbert_model.get_scores(query_words)
        sbert_time = time.time() - start_time
        sbert_times.append(sbert_time)
        
        sbert_top_scores = heapq.nlargest(3, sbert_scores)
        sbert_top_indices = [sbert_scores.index(score) for score in sbert_top_scores]
        
        print(f"SBERT检索时间: {sbert_time:.4f}秒")
        print("SBERT Top-3 结果:")
        for j, (idx, score) in enumerate(zip(sbert_top_indices, sbert_top_scores), 1):
            print(f"  {j}. 文档ID: {idx}, 得分: {score:.4f}")
    
    # 性能统计
    print(f"\n{'='*20} 性能统计对比 {'='*20}")
    
    bm25_avg_time = np.mean(bm25_times)
    sbert_avg_time = np.mean(sbert_times)
    
    print(f"BM25平均检索时间: {bm25_avg_time:.4f}秒")
    print(f"SBERT平均检索时间: {sbert_avg_time:.4f}秒")
    print(f"速度比 (SBERT/BM25): {sbert_avg_time/bm25_avg_time:.2f}x")
    
    print(f"\nBM25检索时间范围: {min(bm25_times):.4f}s - {max(bm25_times):.4f}s")
    print(f"SBERT检索时间范围: {min(sbert_times):.4f}s - {max(sbert_times):.4f}s")
    
    # 详细分析
    print(f"\n{'='*20} 详细性能分析 {'='*20}")
    
    print("BM25算法特点:")
    print("  ✓ 检索速度极快")
    print("  ✓ 内存占用小")
    print("  ✓ 精确关键词匹配效果好")
    print("  ✓ 可解释性强")
    print("  ✗ 无语义理解能力")
    print("  ✗ 无法处理同义词")
    
    print("\nSBERT算法特点:")
    print("  ✓ 强语义理解能力")
    print("  ✓ 支持模糊语义匹配")
    print("  ✓ 多语言支持")
    print("  ✓ 上下文感知")
    print("  ✗ 检索速度较慢")
    print("  ✗ 内存占用大")
    print("  ✗ 可解释性弱")
    
    # 保存结果
    results = {
        'bm25_times': bm25_times,
        'sbert_times': sbert_times,
        'bm25_avg_time': bm25_avg_time,
        'sbert_avg_time': sbert_avg_time,
        'speed_ratio': sbert_avg_time/bm25_avg_time,
        'test_queries': test_queries
    }
    
    with open('experiment_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n实验结果已保存到 experiment_results.json")
    
    return results

def analyze_model_characteristics():
    """分析两个模型的特征"""
    print(f"\n{'='*20} 模型特征分析 {'='*20}")
    
    # 检查模型文件大小
    import os
    
    bm25_size = os.path.getsize(data_path.BM25Model) / (1024*1024)  # MB
    sbert_size = os.path.getsize(data_path.SBERTModel) / (1024*1024)  # MB
    
    print(f"模型文件大小:")
    print(f"  BM25模型: {bm25_size:.2f} MB")
    print(f"  SBERT模型: {sbert_size:.2f} MB")
    print(f"  大小比 (SBERT/BM25): {sbert_size/bm25_size:.2f}x")
    
    print(f"\n内存和计算特点:")
    print(f"  BM25: 稀疏向量，基于统计")
    print(f"  SBERT: 稠密向量，基于神经网络")
    
    print(f"\n适用场景:")
    print(f"  BM25适用: 精确匹配、实时检索、资源受限环境")
    print(f"  SBERT适用: 语义检索、智能问答、跨语言检索")

if __name__ == "__main__":
    print("开始完整的实验分析...")
    
    # 完整性能测试
    results = test_both_models()
    
    # 模型特征分析
    analyze_model_characteristics()
    
    print("\n" + "="*80)
    print("实验分析完成！")
    print("="*80)
