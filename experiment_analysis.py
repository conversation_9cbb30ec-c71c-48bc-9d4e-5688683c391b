#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实验分析脚本 - BM25和SBERT模型性能评估
"""

import pickle
import json
import time
import heapq
import numpy as np
from collections import defaultdict
from BM25 import dealwords, BM25, SBERT
import data_path

def load_test_data():
    """加载测试数据"""
    print("正在加载测试数据...")
    
    # 加载训练数据（用于评估）
    with open(data_path.dataset, encoding='utf-8') as f:
        train_data = [json.loads(line.strip()) for line in f.readlines()]
    
    # 加载新测试数据
    with open(data_path.new_test, encoding='utf-8') as f:
        test_data = [json.loads(line.strip()) for line in f.readlines()]
    
    print(f"训练数据: {len(train_data)} 条")
    print(f"测试数据: {len(test_data)} 条")
    
    return train_data, test_data

def evaluate_bm25_model(train_data, test_data):
    """评估BM25模型性能"""
    print("\n" + "="*60)
    print("BM25模型性能评估")
    print("="*60)
    
    try:
        # 加载BM25模型
        with open(data_path.BM25Model, "rb") as f:
            bm25_model = pickle.load(f)
        print("✓ BM25模型加载成功")
        
        # 在训练数据上评估
        print("\n--- 在训练数据上评估 ---")
        train_results = evaluate_model_on_dataset(bm25_model, train_data[:100], "BM25-训练集")
        
        # 在测试数据上评估
        print("\n--- 在测试数据上评估 ---")
        test_results = evaluate_model_on_dataset(bm25_model, test_data[:50], "BM25-测试集")
        
        return {
            'train': train_results,
            'test': test_results
        }
        
    except Exception as e:
        print(f"✗ BM25模型评估失败: {e}")
        return None

def evaluate_sbert_model(train_data, test_data):
    """评估SBERT模型性能"""
    print("\n" + "="*60)
    print("SBERT模型性能评估")
    print("="*60)
    
    try:
        # 加载SBERT模型
        with open(data_path.SBERTModel, "rb") as f:
            sbert_model = pickle.load(f)
        print("✓ SBERT模型加载成功")
        
        # 在训练数据上评估
        print("\n--- 在训练数据上评估 ---")
        train_results = evaluate_model_on_dataset(sbert_model, train_data[:100], "SBERT-训练集")
        
        # 在测试数据上评估
        print("\n--- 在测试数据上评估 ---")
        test_results = evaluate_model_on_dataset(sbert_model, test_data[:50], "SBERT-测试集")
        
        return {
            'train': train_results,
            'test': test_results
        }
        
    except Exception as e:
        print(f"✗ SBERT模型评估失败: {e}")
        return None

def evaluate_model_on_dataset(model, dataset, model_name):
    """在数据集上评估模型"""
    total_queries = len(dataset)
    total_time = 0
    top1_hits = 0
    top5_hits = 0
    top10_hits = 0
    
    print(f"评估 {model_name}，共 {total_queries} 个查询...")
    
    for i, item in enumerate(dataset):
        if i % 20 == 0:
            print(f"进度: {i}/{total_queries}")
        
        query = item['question']
        if 'answers' in item:
            # 训练数据格式
            relevant_pids = [ans['pid'] for ans in item['answers']]
        else:
            # 测试数据可能没有标准答案，跳过评估
            continue
        
        # 处理查询
        query_words = dealwords(query)
        
        # 计时
        start_time = time.time()
        scores = model.get_scores(query_words)
        end_time = time.time()
        
        total_time += (end_time - start_time)
        
        # 获取top-10结果
        top_scores = heapq.nlargest(10, scores)
        top_indices = [scores.index(score) for score in top_scores]
        
        # 计算命中率
        if len(top_indices) > 0 and top_indices[0] in relevant_pids:
            top1_hits += 1
        
        if any(idx in relevant_pids for idx in top_indices[:5]):
            top5_hits += 1
            
        if any(idx in relevant_pids for idx in top_indices[:10]):
            top10_hits += 1
    
    # 计算指标
    avg_time = total_time / total_queries
    top1_accuracy = top1_hits / total_queries
    top5_accuracy = top5_hits / total_queries
    top10_accuracy = top10_hits / total_queries
    
    results = {
        'total_queries': total_queries,
        'avg_time': avg_time,
        'top1_accuracy': top1_accuracy,
        'top5_accuracy': top5_accuracy,
        'top10_accuracy': top10_accuracy
    }
    
    print(f"\n{model_name} 评估结果:")
    print(f"  查询总数: {total_queries}")
    print(f"  平均检索时间: {avg_time:.4f}秒")
    print(f"  Top-1 准确率: {top1_accuracy:.4f} ({top1_hits}/{total_queries})")
    print(f"  Top-5 准确率: {top5_accuracy:.4f} ({top5_hits}/{total_queries})")
    print(f"  Top-10 准确率: {top10_accuracy:.4f} ({top10_hits}/{total_queries})")
    
    return results

def compare_models_detailed():
    """详细比较两个模型"""
    print("\n" + "="*60)
    print("模型详细比较分析")
    print("="*60)
    
    try:
        # 加载模型
        with open(data_path.BM25Model, "rb") as f:
            bm25_model = pickle.load(f)
        
        # 测试查询
        test_queries = [
            "日莲给他的弟子写了什么？",
            "腾讯公司的总部在哪里？",
            "黄山香烟是哪年创牌的？",
            "中国的首都是哪里？",
            "Python是什么编程语言？"
        ]
        
        print("测试查询列表:")
        for i, query in enumerate(test_queries, 1):
            print(f"  {i}. {query}")
        
        print(f"\n{'='*20} BM25检索结果 {'='*20}")
        
        bm25_times = []
        for i, query in enumerate(test_queries, 1):
            print(f"\n查询 {i}: {query}")
            query_words = dealwords(query)
            print(f"分词结果: {list(query_words)}")
            
            start_time = time.time()
            scores = bm25_model.get_scores(query_words)
            end_time = time.time()
            
            search_time = end_time - start_time
            bm25_times.append(search_time)
            
            top_scores = heapq.nlargest(3, scores)
            top_indices = [scores.index(score) for score in top_scores]
            
            print(f"检索时间: {search_time:.4f}秒")
            print("Top-3 结果:")
            for j, (idx, score) in enumerate(zip(top_indices, top_scores), 1):
                print(f"  {j}. 文档ID: {idx}, 得分: {score:.4f}")
        
        print(f"\nBM25平均检索时间: {np.mean(bm25_times):.4f}秒")
        
        # 尝试加载SBERT模型进行比较
        try:
            with open(data_path.SBERTModel, "rb") as f:
                sbert_model = pickle.load(f)
            
            print(f"\n{'='*20} SBERT检索结果 {'='*20}")
            
            sbert_times = []
            for i, query in enumerate(test_queries, 1):
                print(f"\n查询 {i}: {query}")
                query_words = dealwords(query)
                
                start_time = time.time()
                scores = sbert_model.get_scores(query_words)
                end_time = time.time()
                
                search_time = end_time - start_time
                sbert_times.append(search_time)
                
                top_scores = heapq.nlargest(3, scores)
                top_indices = [scores.index(score) for score in top_scores]
                
                print(f"检索时间: {search_time:.4f}秒")
                print("Top-3 结果:")
                for j, (idx, score) in enumerate(zip(top_indices, top_scores), 1):
                    print(f"  {j}. 文档ID: {idx}, 得分: {score:.4f}")
            
            print(f"\nSBERT平均检索时间: {np.mean(sbert_times):.4f}秒")
            
            # 性能比较
            print(f"\n{'='*20} 性能比较 {'='*20}")
            print(f"BM25平均检索时间: {np.mean(bm25_times):.4f}秒")
            print(f"SBERT平均检索时间: {np.mean(sbert_times):.4f}秒")
            print(f"速度比 (BM25/SBERT): {np.mean(bm25_times)/np.mean(sbert_times):.2f}")
            
        except FileNotFoundError:
            print("\nSBERT模型文件不存在，跳过SBERT比较")
        except Exception as e:
            print(f"\nSBERT模型加载失败: {e}")
        
    except Exception as e:
        print(f"模型比较失败: {e}")

def main():
    """主函数"""
    print("开始实验分析...")
    
    # 加载数据
    train_data, test_data = load_test_data()
    
    # 评估BM25模型
    bm25_results = evaluate_bm25_model(train_data, test_data)
    
    # 评估SBERT模型（如果可用）
    sbert_results = evaluate_sbert_model(train_data, test_data)
    
    # 详细比较
    compare_models_detailed()
    
    print("\n实验分析完成！")

if __name__ == "__main__":
    main()
